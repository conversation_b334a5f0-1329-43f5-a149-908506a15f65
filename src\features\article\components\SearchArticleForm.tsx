import { useTranslation } from 'react-i18next';
import SearchForm from '../../../components/partials/SearchForm';
import { convertDataToSelectOptions } from '../../../utils/common';
import Workflow from '../../../types/Workflow';
import Group from 'types/Group';
import { filter } from 'lodash';
import { SearchField } from '../../../types/common/Search';

interface IProps {
    isLoading: boolean;
    workflows: Workflow[];
    articleTypeId: string;
    showDepartment: boolean;
    departments: Group[];
}

export default function SearchArticleForm({
    isLoading,
    workflows,
    articleTypeId,
    showDepartment,
    departments,
}: Readonly<IProps>) {
    const fields: SearchField[] = [
        { name: 'article_type_id', type: 'hidden', value: articleTypeId, show: true },
        { name: 'title', type: 'text', label: 'Tiêu đề', wrapClassName: 'col-md-4 col-12', show: true },
        {
            name: 'department_id',
            type: 'select',
            label: 'Phòng ban',
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: false,
                choices: convertDataToSelectOptions(departments, 'id', 'name', undefined, null),
            },
            show: showDepartment,
        },
        {
            name: 'workflow_id',
            type: 'select',
            label: 'Trạng thái',
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertDataToSelectOptions(workflows, 'id', 'name', undefined, null),
            },
            show: true,
        },
        {
            name: 'created_at__range',
            type: 'date-range',
            wrapClassName: 'col-md-4 col-12',
            show: true,
            options: {
                placeholderText: 'dd/MM/yyyy',
                dateFormat: 'dd/MM/yyyy',
                isClearable: true,
            },
            label: 'Ngày tạo',
        },
    ];

    return <SearchForm fields={filter(fields, { show: true })} isLoading={isLoading} />;
}
