import { useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import ContentHeader from '../../../components/partials/ContentHeader';
import SearchArticleForm from '../components/SearchArticleForm';
import ArticleTabFilter from '../components/ArticleTabFilter';
import ArticleList from '../components/ArticleList';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { SearchWorkflow, WorkflowQuery, WorkflowTypeNames } from '../../../types/Workflow';
import { LIMIT_MAX, OPERATION_NAME, PAGINATION, QUERY_KEY } from '../../../constants/common';
import { WORKFLOW_LIST } from '../../../services/WorkflowService';
import { keepPreviousData } from '@tanstack/react-query';
import { ArticleType, ArticleTypeNames, ItemStatus } from '../../../types/common/Item';
import isEmpty from 'lodash/isEmpty';
import omitBy from 'lodash/omitBy';
import isUndefined from 'lodash/isUndefined';
import useQueryParams from '../../../hooks/useQueryParams';
import { generateFilters } from '../../../utils/common';
import {
    articleFilterConfig,
    ArticlePageType,
    ArticleQueryRes,
    NewArticleType,
    SearchArticle,
    SearchArticleParam,
} from '../../../types/Article';
import { ARTICLE_LIST } from '../../../services/ArticleService';
import { useAuthStore } from '../../../stores/authStore';
import { USER_LIST } from '../../../services/UserService';
import { UserListQuery, UserRole } from '../../../types/User';
import PaginationTable from '../../../components/partials/PaginationTable';
import { useTranslation } from 'react-i18next';
import { useAppStore } from '../../../stores/appStore';
import { GroupQuery, GroupType, SearchGroup } from 'types/Group';
import { GROUP_LIST } from 'services/GroupService';
import { convertDateRangeToQueryParams } from '../../../utils/date';

export default function ArticlePage() {
    const { t } = useTranslation();
    const { type } = useParams<{ type: ArticlePageType }>();
    const currentUser = useAuthStore((state) => state.user);
    const [activeTab, setActiveTab] = useState('all');
    const isPersonal = type === ArticlePageType.PERSONAL;
    const [displayOptions, setDisplayOptions] = useState<Record<string, boolean>>({});
    const [pageTitle, setPageTitle] = useState('Quản lý bài viết');
    const [workflowTypeIds, setWorkflowTypeIds] = useState<number[]>([]);
    const [userIds, setUserIds] = useState<number[]>([]);
    const [selectedWorkflowIds, setSelectedWorkflowIds] = useState<string[]>([]);
    const { queryParams, setQueryParams } = useQueryParams<SearchArticleParam>();
    const departmentId = useAppStore((state) => state.departmentId);

    const [tabDisplayOptions, setTabDisplayOptions] = useState<Record<string, Record<string, boolean>>>({
        [NewArticleType.ALL]: {},
        [NewArticleType.UNCLASSIFIED]: {},
        [ArticleType.ELECTRONIC.toString()]: {},
        [ArticleType.PAPER.toString()]: {},
        [ArticleType.TELEVISION.toString()]: {},
    });

    useEffect(() => {
        switch (type) {
            case ArticlePageType.PERSONAL:
                setPageTitle('Bài viết cá nhân');
                if (currentUser?.id) {
                    setUserIds([currentUser.id]);
                }
                break;
            case ArticlePageType.REPORTER:
                setPageTitle('Bài viết phóng viên');
                break;
            case ArticlePageType.COLLABORATOR:
                setPageTitle('Bài viết cộng tác viên');
                break;
            default:
                setPageTitle('Quản lý bài viết');
                setUserIds([]);
        }
    }, [type, currentUser]);

    const { data: userData } = useGraphQLQuery<UserListQuery>(
        [QUERY_KEY.USERS, type],
        USER_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            search: '',
            filters: [
                `status_id:=(${ItemStatus.ACTIVE})`,
                ...(type === ArticlePageType.REPORTER ? [`role_id:=(${UserRole.ADMIN})`] : []),
                ...(type === ArticlePageType.COLLABORATOR ? [`role_id:=(${UserRole.CONTRIBUTOR})`] : []),
            ],
        },
        '',
        {
            enabled: type === ArticlePageType.REPORTER || type === ArticlePageType.COLLABORATOR,
            placeholderData: keepPreviousData,
        }
    );

    useEffect(() => {
        if (userData && (type === ArticlePageType.REPORTER || type === ArticlePageType.COLLABORATOR)) {
            const ids = userData.users_list.data.map((user) => user.id ?? 0).filter((id) => id !== 0);
            setUserIds(ids);
        }
    }, [userData, type]);

    const { data: workflowData, refetch: refetchWorkflow } = useGraphQLQuery<WorkflowQuery, SearchWorkflow>(
        [QUERY_KEY.WORKFLOWS, workflowTypeIds],
        WORKFLOW_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            search: '',
            filters: [
                `status_id:=(${ItemStatus.ACTIVE})`,
                `department_id:=(${departmentId})`,
                ...(!isEmpty(workflowTypeIds) ? [`workflow_type_id:[](${workflowTypeIds.join(',')})`] : []),
            ],
            sorts: ['display_order:ASC'],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const workflows = useMemo(() => workflowData?.workflows_list.data ?? [], [workflowData]);

    const { data: groupData } = useGraphQLQuery<GroupQuery, SearchGroup>(
        [QUERY_KEY.GROUPS],
        GROUP_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            filters: [
                `status_id:=(${ItemStatus.ACTIVE})`,
                `department_id:=(${departmentId})`,
                `type_id:=(${GroupType.DEPARTMENT})`,
            ],
        },
        '',
        {
            enabled: !isPersonal,
            placeholderData: keepPreviousData,
        }
    );

    useEffect(() => {
        if (workflows.length > 0 && !queryParams.workflow_id) {
            const ids = workflows.map((workflow) => workflow.id?.toString() ?? '').filter((id) => id !== '');
            setSelectedWorkflowIds(ids);
        }
    }, [workflows, queryParams.workflow_id]);

    useEffect(() => {
        refetchWorkflow();
    }, [workflowTypeIds, refetchWorkflow]);

    const createdByFilter = useMemo(() => {
        if (userIds.length === 0) return undefined;
        return userIds.join(',');
    }, [userIds]);

    const paramConfig: SearchArticleParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            title: queryParams.title,
            workflow_id:
                queryParams.workflow_id ?? (selectedWorkflowIds.length > 0 ? selectedWorkflowIds.join(',') : undefined),
            article_type_id: queryParams.article_type_id,
            created_by: createdByFilter,
            root_article_id:
                activeTab === NewArticleType.ALL || activeTab === NewArticleType.UNCLASSIFIED ? 'null' : undefined,
            is_unclassified: queryParams.is_unclassified,
            created_at_from:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_from ?? undefined,
            created_at_to:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_to ?? undefined,
        },
        isUndefined
    );

    useEffect(() => {
        if (queryParams.article_type_id) {
            setActiveTab(queryParams.article_type_id);
        } else if (queryParams.is_unclassified) {
            setActiveTab(NewArticleType.UNCLASSIFIED);
        } else {
            // Mặc định active tab "Tất cả" nếu không có article_type_id trong query params
            setActiveTab(NewArticleType.ALL);
        }
    }, [queryParams.article_type_id, queryParams.is_unclassified]);

    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, articleFilterConfig);

    const { data, isLoading, refetch } = useGraphQLQuery<ArticleQueryRes, SearchArticle>(
        [QUERY_KEY.ARTICLES, paramConfig, filters],
        ARTICLE_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: filters.length > 0 ? filters : undefined,
        },
        OPERATION_NAME.CALL_STATIC_TOKEN,
        {
            enabled: Boolean(userIds.length > 0),
        }
    );

    const handleTabChange = (tabId: string) => {
        setActiveTab(tabId);

        const params: Record<string, string> = {};

        switch (tabId) {
            case NewArticleType.ALL:
                params.article_type_id = '';
                params.is_unclassified = '';
                break;
            case NewArticleType.UNCLASSIFIED:
                params.is_unclassified = 'true';
                params.article_type_id = '';
                break;
            default:
                if (!isNaN(Number(tabId))) {
                    params.article_type_id = tabId;
                    params.is_unclassified = '';
                }
                break;
        }

        const updatedParams: Partial<SearchArticleParam> = {
            ...params,
        };

        setQueryParams(updatedParams);

        if (tabDisplayOptions[tabId] && Object.keys(tabDisplayOptions[tabId]).length > 0) {
            setDisplayOptions(tabDisplayOptions[tabId]);

            const selectedTypeIds = WorkflowTypeNames.filter(
                (type) => tabDisplayOptions[tabId][type.id.toString()] === true
            ).map((type) => type.id);

            setWorkflowTypeIds(selectedTypeIds.length > 0 ? selectedTypeIds : []);
        } else {
            const newDisplayOptions: Record<string, boolean> = {};

            switch (tabId) {
                case NewArticleType.ALL:
                case NewArticleType.UNCLASSIFIED:
                case ArticleType.ELECTRONIC.toString():
                case ArticleType.PAPER.toString():
                case ArticleType.TELEVISION.toString():
                    WorkflowTypeNames.forEach((type) => {
                        newDisplayOptions[type.id.toString()] = false;
                    });
                    setWorkflowTypeIds([]);
                    break;
                default:
                    break;
            }

            setDisplayOptions(newDisplayOptions);

            setTabDisplayOptions((prev) => ({
                ...prev,
                [tabId]: newDisplayOptions,
            }));
        }
    };

    const handleDisplayOptionChange = (key: string, value: boolean) => {
        const updatedOptions = {
            ...displayOptions,
            [key]: value,
        };
        setDisplayOptions(updatedOptions);

        setTabDisplayOptions((prev) => ({
            ...prev,
            [activeTab]: updatedOptions,
        }));
    };

    const handleWorkflowTypeChange = (typeIds: number[]) => {
        setWorkflowTypeIds(typeIds.length > 0 ? typeIds : []);

        const updatedOptions = { ...displayOptions };

        WorkflowTypeNames.forEach((type) => {
            updatedOptions[type.id.toString()] = false;
        });

        typeIds.forEach((id) => {
            updatedOptions[id.toString()] = true;
        });

        setDisplayOptions(updatedOptions);

        setTabDisplayOptions((prev) => ({
            ...prev,
            [activeTab]: updatedOptions,
        }));
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    return (
        <>
            <Helmet>
                <title>{pageTitle}</title>
            </Helmet>
            <ContentHeader
                title={pageTitle}
                contextMenu={ArticleTypeNames.map((item) => ({
                    text: `Thêm bài viết ${t(`${item.name}.single`)}`,
                    to: `/article/add/${type}/${item.id}`,
                    icon: 'PLUS',
                }))}
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchArticleForm
                        isLoading={isLoading}
                        workflows={workflows}
                        articleTypeId={activeTab === NewArticleType.ALL ? '' : activeTab}
                        showDepartment={!isPersonal}
                        departments={groupData?.groups_list.data ?? []}
                    />

                    <div className="mt-2">
                        <ArticleTabFilter
                            activeTab={activeTab}
                            onTabChange={handleTabChange}
                            displayOptions={displayOptions}
                            onDisplayOptionChange={handleDisplayOptionChange}
                            onWorkflowTypeChange={handleWorkflowTypeChange}
                        />
                    </div>

                    <div className="mt-2">
                        {data && (
                            <div className="card">
                                <ArticleList
                                    activeTab={activeTab}
                                    displayOptions={displayOptions}
                                    isLoading={isLoading}
                                    data={data}
                                    limit={Number(paramConfig.limit)}
                                    refetch={refetch}
                                    type={type ?? ArticlePageType.PERSONAL}
                                    articleTypeId={Number(activeTab)}
                                />
                                <PaginationTable
                                    countItem={data.articles_list.totalCount}
                                    totalPage={data.articles_list.totalPages}
                                    currentPage={data.articles_list.currentPage}
                                    handlePageChange={handlePageChange}
                                />
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </>
    );
}
